import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { QRCodeSVG } from 'qrcode.react';
import { ExternalLink, CreditCard, Smartphone } from 'lucide-react';
import { usePayoneer } from '../hooks/usePayoneer';
import { formatCurrency, generateOrderId } from '../utils/paymentUtils';

interface PayoneerPaymentFormProps {
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  className?: string;
}

const PayoneerPaymentForm: React.FC<PayoneerPaymentFormProps> = ({
  onSuccess,
  onError,
  className = '',
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [payerName, setPayerName] = useState('');
  const [payerEmail, setPayerEmail] = useState('');
  const [selectedCurrency] = useState('GBP');
  const [showOptions, setShowOptions] = useState(false);
  // const [paymentData, setPaymentData] = useState<any>(null);

  const { createPayment, getPaymentStatus, isLoading } = usePayoneer();

  const getSuccessUrl = () => `${window.location.origin}/payment/success`;
  const getCancelUrl = () => `${window.location.origin}/payment/cancel`;

  const handleAmountChange = (value: string) => {
    const numericValue = parseFloat(value) || 0;
    const amountInCents = Math.round(numericValue * 100);
    setAmount(amountInCents);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    setShowOptions(true);
  };

  const handlePaymentUrl = async () => {
    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      if (result.success && result.data) {
        console.log('=== PAYONEER PAYMENT URL GENERATED ===');
        console.log('Payment URL:', result.data.redirect_url);
        console.log('Payment ID:', result.data.payment_id);
        console.log('Order ID:', result.data.order_id);
        console.log('=== END PAYONEER PAYMENT URL ===');

        // Show confirmation popup before opening URL
        const userConfirmed = window.confirm(
          'Payment URL generated successfully!\n\n' +
          'Click "OK" to open the payment page in a new tab, or "Cancel" to copy the URL to clipboard.'
        );

        if (userConfirmed) {
          // Try to open in new tab
          const newWindow = window.open(result.data.redirect_url, '_blank', 'noopener,noreferrer');

          // Check if popup was blocked
          if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            // Popup was blocked, show alternative
            const fallbackConfirmed = window.confirm(
              'Popup blocked by browser!\n\n' +
              'Click "OK" to copy the payment URL to clipboard, then paste it in a new tab.'
            );

            if (fallbackConfirmed) {
              await navigator.clipboard.writeText(result.data.redirect_url);
              toast.success('Payment URL copied to clipboard! Please paste it in a new tab.');
            }
          } else {
            toast.success('Payment page opened in new tab');
          }
        } else {
          // User chose to copy URL
          await navigator.clipboard.writeText(result.data.redirect_url);
          toast.success('Payment URL copied to clipboard!');
        }

        // Reset form state
        setShowOptions(false);
      } else {
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };

  const handleQRCode = async () => {
    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount, // Use raw amount in cents
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'qr', // Request QR code type
      });

      if (result.success && result.data) {
        // Show confirmation popup before opening URL
        const userConfirmed = window.confirm(
          'Payment QR code generated successfully!\n\n' +
          'Click "OK" to open the payment page in a new tab, or "Cancel" to copy the URL to clipboard.'
        );

        if (userConfirmed) {
          // Try to open in new tab
          const newWindow = window.open(result.data.payment_url, '_blank', 'noopener,noreferrer');

          // Check if popup was blocked
          if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            // Popup was blocked, show alternative
            const fallbackConfirmed = window.confirm(
              'Popup blocked by browser!\n\n' +
              'Click "OK" to copy the payment URL to clipboard, then paste it in a new tab.'
            );

            if (fallbackConfirmed) {
              await navigator.clipboard.writeText(result.data.payment_url);
              toast.success('Payment URL copied to clipboard! Please paste it in a new tab.');
            }
          } else {
            toast.success('Payment page opened in new tab');
          }
        } else {
          // User chose to copy URL
          await navigator.clipboard.writeText(result.data.payment_url);
          toast.success('Payment URL copied to clipboard!');
        }

        setShowOptions(false);
      } else {
        toast.error(result.message || 'Failed to generate QR code');
      }
    } catch (error) {
      console.error('QR generation error:', error);
      toast.error('Failed to generate QR code');
    }
  };

  // const copyToClipboard = async (text: string) => {
  //   try {
  //     await navigator.clipboard.writeText(text);
  //     toast.success('Copied to clipboard!');
  //   } catch (error) {
  //     toast.error('Failed to copy to clipboard');
  //   }
  // };

  // const handleSuccess = (result: any) => {
  //   toast.success('Payment completed successfully!');
  //   onSuccess?.(result);
  // };

  // const handleError = (error: any) => {
  //   toast.error('Payment failed');
  //   onError?.(error);
  // };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
          <CreditCard className="w-5 h-5 text-orange-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Payoneer Payment</h3>
          <p className="text-sm text-gray-500">Global payment processing</p>
        </div>
      </div>

      {!showOptions ? (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Amount ({selectedCurrency})
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              onChange={(e) => handleAmountChange(e.target.value)}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payer Name
            </label>
            <input
              type="text"
              placeholder="Enter payer name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={payerName}
              onChange={(e) => setPayerName(e.target.value)}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email (Optional)
            </label>
            <input
              type="email"
              placeholder="Enter email address"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={payerEmail}
              onChange={(e) => setPayerEmail(e.target.value)}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Processing...' : `Continue with ${formatCurrency(amount, selectedCurrency)}`}
          </button>
        </form>
      ) : (
        <div className="space-y-4">
          <div className="text-center">
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              Payment Amount: {formatCurrency(amount, selectedCurrency)}
            </h4>
            <p className="text-sm text-gray-600 mb-4">Choose your payment method</p>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={handlePaymentUrl}
              disabled={isLoading}
              className="flex items-center justify-center gap-3 w-full bg-orange-600 text-white py-3 px-4 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ExternalLink className="w-5 h-5" />
              {isLoading ? 'Generating...' : 'Open Payment Page'}
            </button>

            <button
              onClick={handleQRCode}
              disabled={isLoading}
              className="flex items-center justify-center gap-3 w-full bg-gray-600 text-white py-3 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Smartphone className="w-5 h-5" />
              {isLoading ? 'Generating...' : 'Generate QR Code'}
            </button>
          </div>

          <button
            onClick={() => setShowOptions(false)}
            className="w-full text-gray-600 py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Back to Form
          </button>
        </div>
      )}
    </div>
  );
};

export default PayoneerPaymentForm;
