import { lazy, object, optional, Schema, string } from '../schema';
import { Card, cardSchema } from './card';

/** Represents additional details of a tender with `type` `CARD` or `SQUARE_GIFT_CARD` */
export interface TenderCardDetails {
  /** Indicates the card transaction's current status. */
  status?: string;
  /**
   * Represents the payment details of a card to be used for payments. These
   * details are determined by the payment token generated by Web Payments SDK.
   */
  card?: Card;
  /** Indicates the method used to enter the card's details. */
  entryMethod?: string;
}

export const tenderCardDetailsSchema: Schema<TenderCardDetails> = object({
  status: ['status', optional(string())],
  card: ['card', optional(lazy(() => cardSchema))],
  entryMethod: ['entry_method', optional(string())],
});
