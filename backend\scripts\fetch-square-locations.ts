#!/usr/bin/env tsx

/**
 * Square Locations Fetcher Script (TypeScript)
 * 
 * This script fetches all Square locations for your account
 * and displays them in a readable format.
 * 
 * Usage:
 *   npx tsx scripts/fetch-square-locations.ts
 *   or
 *   npm run square:locations:ts
 */

import { Client, Environment } from 'square';
import { env } from '../src/config/env';

interface LocationInfo {
  id: string;
  name?: string;
  status?: string;
  type?: string;
  address?: any;
  timezone?: string;
  capabilities?: string[];
}

async function fetchSquareLocations(): Promise<void> {
  console.log('🔍 Fetching Square Locations...\n');

  try {
    const accessToken = env.SQUARE_ACCESS_TOKEN;
    const applicationId = env.SQUARE_APPLICATION_ID;
    const environment = env.SQUARE_ENVIRONMENT;

    if (!accessToken || accessToken === 'your_square_access_token') {
      console.error('❌ SQUARE_ACCESS_TOKEN not found or not set in .env file');
      process.exit(1);
    }

    if (!applicationId || applicationId === 'your_square_application_id') {
      console.error('❌ SQUARE_APPLICATION_ID not found or not set in .env file');
      process.exit(1);
    }

    console.log('📋 Configuration:');
    console.log(`   Environment: ${environment}`);
    console.log(`   Application ID: ${applicationId}`);
    console.log(`   Access Token: ${accessToken.substring(0, 10)}...`);
    console.log('');

    // Initialize Square client
    const client = new Client({
      accessToken: accessToken,
      environment: environment === 'production' ? Environment.Production : Environment.Sandbox,
    });

    const { locationsApi } = client;

    // Fetch locations
    console.log('🌐 Calling Square Locations API...');
    const response = await locationsApi.listLocations();

    if (response.result.errors && response.result.errors.length > 0) {
      console.error('❌ Square API Error:');
      response.result.errors.forEach(error => {
        console.error(`   ${error.category}: ${error.detail}`);
      });
      process.exit(1);
    }

    const locations = response.result.locations || [];

    if (locations.length === 0) {
      console.log('⚠️  No locations found for your Square account');
      return;
    }

    console.log(`✅ Found ${locations.length} location(s):\n`);

    // Display locations in a readable format
    locations.forEach((location, index) => {
      console.log(`📍 Location ${index + 1}:`);
      console.log(`   ID: ${location.id}`);
      console.log(`   Name: ${location.name || 'N/A'}`);
      console.log(`   Status: ${location.status || 'N/A'}`);
      console.log(`   Type: ${location.type || 'N/A'}`);
      
      if (location.address) {
        const addr = location.address;
        console.log(`   Address: ${addr.addressLine1 || ''} ${addr.addressLine2 || ''}`.trim());
        console.log(`   City: ${addr.locality || 'N/A'}`);
        console.log(`   State: ${addr.administrativeDistrictLevel1 || 'N/A'}`);
        console.log(`   Country: ${addr.country || 'N/A'}`);
        console.log(`   Postal Code: ${addr.postalCode || 'N/A'}`);
      }
      
      if (location.timezone) {
        console.log(`   Timezone: ${location.timezone}`);
      }
      
      if (location.capabilities && location.capabilities.length > 0) {
        console.log(`   Capabilities: ${location.capabilities.join(', ')}`);
      }
      
      console.log('');
    });

    // Show current configuration
    const currentLocationId = env.SQUARE_LOCATION_ID;
    if (currentLocationId && currentLocationId !== 'your_square_location_id') {
      console.log(`🔧 Current SQUARE_LOCATION_ID in .env: ${currentLocationId}`);
      
      const currentLocation = locations.find(loc => loc.id === currentLocationId);
      if (currentLocation) {
        console.log(`   ✅ This location exists: ${currentLocation.name || 'Unnamed'}`);
      } else {
        console.log(`   ⚠️  This location ID was not found in your account`);
      }
    } else {
      console.log('🔧 To use Square payments, add one of these location IDs to your .env file:');
      console.log('   SQUARE_LOCATION_ID=<location_id>');
    }

    // Show recommended location
    const activeLocations = locations.filter(loc => loc.status === 'ACTIVE');
    if (activeLocations.length > 0) {
      const recommended = activeLocations[0];
      console.log(`\n💡 Recommended location ID: ${recommended.id}`);
      console.log(`   Name: ${recommended.name || 'Unnamed'}`);
      console.log(`   Add this to your .env file: SQUARE_LOCATION_ID=${recommended.id}`);
    }

    console.log('\n✨ Done!');

  } catch (error: any) {
    console.error('❌ Error fetching Square locations:');
    console.error(error.message);
    
    if (error.errors) {
      error.errors.forEach((err: any) => {
        console.error(`   ${err.category}: ${err.detail}`);
      });
    }
    
    process.exit(1);
  }
}

// Export for use in other modules
export { fetchSquareLocations };

// Run the script if called directly
if (require.main === module) {
  fetchSquareLocations();
}
