import { Schema } from '../schema';
import { Card } from './card';
import { Error } from './error';
/**
 * Defines the fields that are included in the response body of
 * a request to the [RetrieveCard]($e/Cards/RetrieveCard) endpoint.
 * Note: if there are errors processing the request, the card field will not be
 * present.
 */
export interface RetrieveCardResponse {
    /** Information on errors encountered during the request. */
    errors?: Error[];
    /**
     * Represents the payment details of a card to be used for payments. These
     * details are determined by the payment token generated by Web Payments SDK.
     */
    card?: Card;
}
export declare const retrieveCardResponseSchema: Schema<RetrieveCardResponse>;
