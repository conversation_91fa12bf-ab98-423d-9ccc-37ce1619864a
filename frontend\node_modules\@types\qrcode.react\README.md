# Installation
> `npm install --save @types/qrcode.react`

# Summary
This package contains type definitions for qrcode.react (https://github.com/zpao/qrcode.react).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qrcode.react.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qrcode.react/index.d.ts)
````ts
/// <reference types="react" />

declare namespace qrcode {
    interface ImageSettings {
        src: string;
        x?: number | undefined;
        y?: number | undefined;
        height?: number | undefined;
        width?: number | undefined;
        excavate?: boolean | undefined;
    }

    interface BaseQRCodeProps {
        value: string;
        size?: number | undefined;
        includeMargin?: boolean | undefined;
        bgColor?: string | undefined;
        fgColor?: string | undefined;
        level?: "L" | "M" | "Q" | "H" | undefined;
        imageSettings?: ImageSettings | undefined;
    }

    type CanvasQRCodeProps = BaseQRCodeProps & {
        renderAs?: "canvas" | undefined;
    } & React.CanvasHTMLAttributes<HTMLCanvasElement>;

    type SvgQRCodeProps = BaseQRCodeProps & {
        renderAs: "svg";
    } & React.SVGProps<SVGSVGElement>;

    type QRCode = React.ComponentClass<CanvasQRCodeProps | SvgQRCodeProps>;
}

declare const qrcode: qrcode.QRCode;
export = qrcode;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/react](https://npmjs.com/package/@types/react)

# Credits
These definitions were written by [Mleko](https://github.com/mleko), [Yonas](https://github.com/yonasadiel), and [Bjoluc](https://github.com/bjoluc).
