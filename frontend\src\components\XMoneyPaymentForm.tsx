/**
 * XMoney Payment Form Component
 *
 * Provides XMoney Payment Gateway integration for cryptocurrency and digital payments
 * Maintains POS terminal UI design with mobile responsiveness
 */

import { useState } from 'react';
import { toast } from 'sonner';
import { useXMoneyPaymentFlow, useXMoneyPaymentUtils } from '../hooks/useXMoney';
import XMoneyPaymentOverlay from './XMoneyPaymentOverlay';
import { AndroidNumericKeypad } from './AndroidNumericKeypad';

interface XMoneyPaymentFormProps {
  amount?: number; // Amount in cents (optional, defaults to 1000)
  currency?: string;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  customerData?: {
    firstName?: string;
    lastName?: string;
    email?: string;
  };
  className?: string;
}

export function XMoneyPaymentForm({
  amount: initialAmount = 0,
  currency = 'GBP',
  onSuccess,
  onError,
  onCancel,
  customerData,
  className = ''
}: XMoneyPaymentFormProps) {
  const [amount, setAmount] = useState<number>(initialAmount);
  const [amountInput, setAmountInput] = useState<string>(
    initialAmount > 0 ? (initialAmount / 100).toFixed(2) : ''
  );
  const [selectedCurrency, setSelectedCurrency] = useState<string>(currency);
  const [payerName, setPayerName] = useState<string>(
    customerData?.firstName && customerData?.lastName 
      ? `${customerData.firstName} ${customerData.lastName}` 
      : ''
  );
  const [payerEmail, setPayerEmail] = useState<string>(customerData?.email || '');
  const [paymentData, setPaymentData] = useState<any>(null);
  const [showOverlay, setShowOverlay] = useState(false);
  const [showOptions, setShowOptions] = useState(false);

  const { createPayment, isLoading, error } = useXMoneyPaymentFlow();
  const { 
    generateOrderId, 
    getSuccessUrl, 
    getCancelUrl, 
    getSupportedCurrencies,
    getSupportedCryptocurrencies,
    getSupportedFiatCurrencies,
    isCryptocurrency,
    isFiatCurrency
  } = useXMoneyPaymentUtils();

  const supportedCurrencies = getSupportedCurrencies();
  const cryptoCurrencies = getSupportedCryptocurrencies();
  const fiatCurrencies = getSupportedFiatCurrencies();

  // Helper function to handle amount changes from keyboard
  const handleAmountChange = (value: string) => {
    setAmountInput(value);
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue >= 0) {
      setAmount(Math.round(numericValue * 100)); // Convert to cents
    }
  };

  const handleSuccess = (result: any) => {
    toast.success('Payment completed successfully!');
    setShowOverlay(false);
    setPaymentData(null);
    onSuccess?.(result);
  };

  const handleError = (error: any) => {
    toast.error(`Payment failed: ${error.message || 'Unknown error'}`);
    setShowOverlay(false);
    setPaymentData(null);
    onError?.(error);
  };

  const handleCancel = () => {
    setShowOverlay(false);
    setPaymentData(null);
    onCancel?.();
  };

  const handleGetQR = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (isLoading) {
      console.log('Request already in progress, ignoring duplicate call');
      return;
    }

    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount, // Use raw amount in cents
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'qr', // Request QR code type
      });

      if (result.success && result.data) {
        // Open payment URL in new tab instead of showing overlay
        window.open(result.data.payment_url, '_blank');
        toast.success('Payment opened in new tab!');
        setShowOptions(false);
      } else {
        toast.error(result.message || 'Failed to generate QR code');
      }
    } catch (error) {
      console.error('QR generation error:', error);
      toast.error('Failed to generate QR code');
    }
  };

  const handleGetURL = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (isLoading) {
      console.log('Request already in progress, ignoring duplicate call');
      return;
    }

    try {
      // Loading state is managed by the hook
      const orderId = generateOrderId();

      console.log('=== CREATING XMONEY PAYMENT URL ===');
      console.log('Order ID:', orderId);
      console.log('Amount:', amount);
      console.log('Currency:', selectedCurrency);
      console.log('Payer Name:', payerName.trim());

      const result = await createPayment({
        orderId,
        amount: amount, // Use raw amount in cents
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url', // Request payment URL type
      });

      if (result.success && result.data && result.data.payment_url) {
        console.log('=== OPENING PAYMENT URL IN NEW TAB ===');
        console.log('URL:', result.data.payment_url);
        
        // Open payment URL in new tab instead of showing overlay
        window.open(result.data.payment_url, '_blank', 'noopener,noreferrer');
        
        toast.success('Payment page opened in new tab');
        onSuccess?.(result.data);
      } else {
        toast.error(result.message || 'Failed to create payment URL');
      }
    } catch (error) {
      console.error('URL generation error:', error);
      toast.error('Failed to create payment URL');
    }
  };

  // Show overlay if payment data exists
  if (showOverlay && paymentData) {
    return (
      <XMoneyPaymentOverlay
        paymentData={paymentData}
        amount={amount}
        currency={selectedCurrency}
        onSuccess={handleSuccess}
        onError={handleError}
        onCancel={handleCancel}
        className={className}
      />
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">XMoney Payment</h2>
        <p className="text-gray-600">Cryptocurrency and digital payment processing</p>
      </div>

      {/* Currency Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Currency
        </label>
        <select
          value={selectedCurrency}
          onChange={(e) => setSelectedCurrency(e.target.value)}
          className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={isLoading}
        >
          <optgroup label="Fiat Currencies">
            {fiatCurrencies.map(curr => (
              <option key={curr} value={curr}>{curr}</option>
            ))}
          </optgroup>
          <optgroup label="Cryptocurrencies">
            {cryptoCurrencies.map(curr => (
              <option key={curr} value={curr}>{curr}</option>
            ))}
          </optgroup>
        </select>
        {isCryptocurrency(selectedCurrency) && (
          <p className="text-xs text-blue-600 mt-1">
            💰 Cryptocurrency payment selected
          </p>
        )}
      </div>

      {/* Amount Input */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Amount ({selectedCurrency})
        </label>
        <div className="relative">
          <input
            type="text"
            value={amountInput}
            onChange={(e) => handleAmountChange(e.target.value)}
            placeholder="0.00"
            className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
          <span className="absolute right-3 top-3 text-gray-500 text-lg">
            {selectedCurrency}
          </span>
        </div>
      </div>

      {/* Virtual Keyboard */}
      <div className="mb-6">
        <AndroidNumericKeypad
          value={amountInput}
          onChange={handleAmountChange}
          placeholder="Enter amount"
          disabled={isLoading}
        />
      </div>

      {/* Payer Name Input */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Payer Name *
        </label>
        <input
          type="text"
          value={payerName}
          onChange={(e) => setPayerName(e.target.value)}
          placeholder="Enter payer name"
          className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={isLoading}
          required
        />
      </div>

      {/* Payer Email Input (Optional) */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Email (Optional)
        </label>
        <input
          type="email"
          value={payerEmail}
          onChange={(e) => setPayerEmail(e.target.value)}
          placeholder="Enter email address"
          className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={isLoading}
        />
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        <button
          onClick={handleGetQR}
          disabled={isLoading || !payerName.trim() || amount <= 0}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium transition-colors"
        >
          {isLoading ? 'Generating...' : 'Generate QR Code'}
        </button>

        <button
          onClick={handleGetURL}
          disabled={isLoading || !payerName.trim() || amount <= 0}
          className="w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium transition-colors"
        >
          {isLoading ? 'Creating...' : 'Open Payment Page'}
        </button>

        {onCancel && (
          <button
            onClick={onCancel}
            disabled={isLoading}
            className="w-full bg-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-400 disabled:cursor-not-allowed font-medium transition-colors"
          >
            Cancel
          </button>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
          <p className="text-sm">
            {error instanceof Error ? error.message : 'An error occurred'}
          </p>
        </div>
      )}
    </div>
  );
}
