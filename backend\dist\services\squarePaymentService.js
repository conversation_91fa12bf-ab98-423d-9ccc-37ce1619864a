"use strict";
/**
 * Square Payment Service
 *
 * Production-level Square Web Payments SDK and Payments API integration
 * Implements card payments, payment links, and secure payment processing
 * https://developer.squareup.com/docs/web-payments/overview
 * https://developer.squareup.com/docs/payments-api/take-payments/card-payments
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.squarePaymentService = void 0;
const square_1 = require("square");
const axios_1 = __importDefault(require("axios"));
const zod_1 = require("zod");
const crypto_1 = require("crypto");
const env_1 = require("../config/env");
const logger_1 = require("../config/logger");
const squareLogger = (0, logger_1.createChildLogger)({ module: 'square-payment-service' });
// Validation schemas
const createPaymentSchema = zod_1.z.object({
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('USD'),
    successUrl: zod_1.z.string().url(),
    cancelUrl: zod_1.z.string().url(),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    languageCode: zod_1.z.string().length(2).default('en'),
});
const createPaymentTokenSchema = zod_1.z.object({
    sourceId: zod_1.z.string().min(1), // Payment token from Web Payments SDK
    orderId: zod_1.z.string().min(1),
    amount: zod_1.z.number().positive().int(),
    currency: zod_1.z.string().length(3).default('USD'),
    payerName: zod_1.z.string().min(1),
    payerEmail: zod_1.z.string().email().optional(),
    locationId: zod_1.z.string().optional(),
});
/**
 * Square Payment Service Class
 * Uses official Square Node.js SDK for production-level integration
 */
class SquarePaymentService {
    constructor() {
        this.accessToken = env_1.env.SQUARE_ACCESS_TOKEN || '';
        this.applicationId = env_1.env.SQUARE_APPLICATION_ID || '';
        this.environment = env_1.env.SQUARE_ENVIRONMENT || 'sandbox';
        this.locationId = env_1.env.SQUARE_LOCATION_ID || '';
        // Initialize Square SDK client
        this.client = new square_1.Client({
            accessToken: this.accessToken,
            environment: this.environment === 'production' ? square_1.Environment.Production : square_1.Environment.Sandbox,
        });
        // Initialize HTTP client for direct API calls
        const baseUrl = this.environment === 'production'
            ? 'https://connect.squareup.com/v2'
            : 'https://connect.squareupsandbox.com/v2';
        this.httpClient = axios_1.default.create({
            baseURL: baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.accessToken}`,
                'Square-Version': '2023-10-18',
            },
        });
        squareLogger.info('Square Payment Service initialized', {
            environment: this.environment,
            applicationId: this.applicationId.substring(0, 10) + '...',
            hasAccessToken: !!this.accessToken,
            locationId: this.locationId,
        });
    }
    /**
     * Create a new Square payment
     */
    async createPayment(params) {
        try {
            const validatedParams = createPaymentSchema.parse(params);
            squareLogger.info('Creating Square payment', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
                locationId: this.locationId,
            });
            // Create payment link using HTTP API
            const requestData = {
                idempotency_key: (0, crypto_1.randomUUID)(),
                checkout_options: {
                    redirect_url: validatedParams.successUrl,
                    ask_for_shipping_address: false,
                },
                order: {
                    location_id: this.locationId,
                    line_items: [
                        {
                            name: `Payment for order ${validatedParams.orderId}`,
                            quantity: '1',
                            base_price_money: {
                                amount: validatedParams.amount,
                                currency: validatedParams.currency,
                            },
                        },
                    ],
                },
                payment_note: `Payment for ${validatedParams.payerName}`,
                merchant_support_email: validatedParams.payerEmail || '<EMAIL>',
            };
            // Log the request being sent to Square
            console.log('=== SQUARE API REQUEST ===');
            console.log('URL:', '/online-checkout/payment-links');
            console.log('Method:', 'POST');
            console.log('Request Data:', JSON.stringify(requestData, null, 2));
            console.log('========================');
            const response = await this.httpClient.post('/online-checkout/payment-links', requestData);
            // Log the complete Square API response
            console.log('=== SQUARE API RESPONSE ===');
            console.log('Status:', response.status);
            console.log('Status Text:', response.statusText);
            console.log('Response Data:', JSON.stringify(response.data, null, 2));
            console.log('==========================');
            if (response.data.errors && response.data.errors.length > 0) {
                squareLogger.error('Square payment link creation failed', {
                    errors: response.data.errors,
                });
                return {
                    success: false,
                    error: 'PAYMENT_LINK_CREATION_FAILED',
                    message: response.data.errors[0].detail || 'Failed to create payment link',
                    details: response.data.errors,
                };
            }
            const paymentLink = response.data.payment_link;
            if (!paymentLink || !paymentLink.url) {
                return {
                    success: false,
                    error: 'PAYMENT_URL_MISSING',
                    message: 'Payment link created but URL is missing',
                };
            }
            const paymentResponse = {
                id: paymentLink.id || (0, crypto_1.randomUUID)(),
                status: 'pending',
                checkout_url: paymentLink.url,
                payment_url: paymentLink.url,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                application_id: this.applicationId,
            };
            squareLogger.info('Square payment link created successfully', {
                paymentLinkId: paymentLink.id,
                orderId: validatedParams.orderId,
                url: paymentLink.url,
            });
            return {
                success: true,
                data: paymentResponse,
            };
        }
        catch (error) {
            squareLogger.error('Square payment creation failed', error);
            if (error instanceof zod_1.z.ZodError) {
                return {
                    success: false,
                    error: 'VALIDATION_ERROR',
                    message: 'Invalid payment parameters',
                    details: error.errors,
                };
            }
            if (error instanceof square_1.ApiError) {
                return {
                    success: false,
                    error: 'SQUARE_API_ERROR',
                    message: error.errors?.[0]?.detail || error.message,
                    details: error.errors,
                };
            }
            if (axios_1.default.isAxiosError(error)) {
                return {
                    success: false,
                    error: 'SQUARE_HTTP_ERROR',
                    message: error.response?.data?.errors?.[0]?.detail || error.message,
                    details: error.response?.data,
                };
            }
            return {
                success: false,
                error: 'INTERNAL_ERROR',
                message: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Process payment token from Web Payments SDK
     * Creates a payment using a token generated by the frontend
     */
    async processPaymentToken(params) {
        try {
            const validatedParams = createPaymentTokenSchema.parse(params);
            squareLogger.info('Processing Square payment token', {
                orderId: validatedParams.orderId,
                amount: validatedParams.amount,
                currency: validatedParams.currency,
                payerName: validatedParams.payerName,
            });
            const { paymentsApi } = this.client;
            const requestBody = {
                sourceId: validatedParams.sourceId,
                idempotencyKey: (0, crypto_1.randomUUID)(),
                amountMoney: {
                    amount: BigInt(validatedParams.amount),
                    currency: validatedParams.currency,
                },
                note: `Payment for order ${validatedParams.orderId}`,
                buyerEmailAddress: validatedParams.payerEmail,
                referenceId: validatedParams.orderId,
            };
            const response = await paymentsApi.createPayment(requestBody);
            if (response.result.errors && response.result.errors.length > 0) {
                squareLogger.error('Square payment processing failed', {
                    errors: response.result.errors,
                });
                return {
                    success: false,
                    error: 'PAYMENT_PROCESSING_FAILED',
                    message: response.result.errors[0].detail || 'Failed to process payment',
                    details: response.result.errors,
                };
            }
            const payment = response.result.payment;
            if (!payment) {
                return {
                    success: false,
                    error: 'PAYMENT_MISSING',
                    message: 'Payment processed but result is missing',
                };
            }
            squareLogger.info('Square payment processed successfully', {
                paymentId: payment.id,
                orderId: validatedParams.orderId,
                status: payment.status,
            });
            return {
                success: true,
                data: {
                    id: payment.id,
                    status: payment.status,
                    amount: Number(payment.amountMoney?.amount || 0),
                    currency: payment.amountMoney?.currency,
                    receipt_url: payment.receiptUrl,
                    created_at: payment.createdAt,
                },
            };
        }
        catch (error) {
            squareLogger.error('Failed to process Square payment token', error);
            if (error instanceof square_1.ApiError) {
                return {
                    success: false,
                    error: 'SQUARE_API_ERROR',
                    message: error.errors?.[0]?.detail || error.message,
                    details: error.errors,
                };
            }
            return {
                success: false,
                error: 'PAYMENT_PROCESSING_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Get payment status
     */
    async getPaymentStatus(paymentId) {
        try {
            squareLogger.info('Getting Square payment status', { paymentId });
            const { paymentsApi } = this.client;
            const response = await paymentsApi.getPayment(paymentId);
            if (response.result.errors && response.result.errors.length > 0) {
                return {
                    success: false,
                    error: 'PAYMENT_NOT_FOUND',
                    message: response.result.errors[0].detail || 'Payment not found',
                    details: response.result.errors,
                };
            }
            return {
                success: true,
                data: response.result.payment,
            };
        }
        catch (error) {
            squareLogger.error('Failed to get Square payment status', error);
            if (error instanceof square_1.ApiError) {
                return {
                    success: false,
                    error: 'SQUARE_API_ERROR',
                    message: error.errors?.[0]?.detail || error.message,
                    details: error.errors,
                };
            }
            return {
                success: false,
                error: 'STATUS_RETRIEVAL_FAILED',
                message: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
    /**
     * Health check for Square service
     */
    async healthCheck() {
        try {
            // Simple health check - verify credentials are configured
            if (!this.accessToken || !this.applicationId) {
                return {
                    success: false,
                    error: 'CONFIGURATION_ERROR',
                    message: 'Square credentials not configured',
                };
            }
            // Try to get locations to verify API connectivity
            try {
                const { locationsApi } = this.client;
                const response = await locationsApi.listLocations();
                if (response.result.errors && response.result.errors.length > 0) {
                    return {
                        success: false,
                        error: 'API_CONNECTION_FAILED',
                        message: 'Failed to connect to Square API',
                        details: response.result.errors,
                    };
                }
                return {
                    success: true,
                    data: {
                        status: 'healthy',
                    },
                };
            }
            catch (error) {
                return {
                    success: false,
                    error: 'API_CONNECTION_FAILED',
                    message: 'Failed to connect to Square API',
                    details: error instanceof Error ? error.message : 'Unknown error',
                };
            }
        }
        catch (error) {
            return {
                success: false,
                error: 'HEALTH_CHECK_FAILED',
                message: 'Square service health check failed',
            };
        }
    }
    /**
     * Generate QR code data for payment
     */
    generateQRCodeData(checkoutUrl) {
        return checkoutUrl;
    }
    /**
     * Get supported payment methods
     */
    getSupportedPaymentMethods() {
        return ['card', 'digital_wallet', 'ach_bank_transfer', 'buy_now_pay_later'];
    }
    /**
     * Get supported currencies
     */
    getSupportedCurrencies() {
        return ['USD', 'CAD', 'GBP', 'EUR', 'AUD', 'JPY'];
    }
}
exports.squarePaymentService = new SquarePaymentService();
//# sourceMappingURL=squarePaymentService.js.map