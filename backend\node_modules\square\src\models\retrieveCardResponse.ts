import { array, lazy, object, optional, Schema } from '../schema';
import { Card, cardSchema } from './card';
import { Error, errorSchema } from './error';

/**
 * Defines the fields that are included in the response body of
 * a request to the [RetrieveCard]($e/Cards/RetrieveCard) endpoint.
 * Note: if there are errors processing the request, the card field will not be
 * present.
 */
export interface RetrieveCardResponse {
  /** Information on errors encountered during the request. */
  errors?: Error[];
  /**
   * Represents the payment details of a card to be used for payments. These
   * details are determined by the payment token generated by Web Payments SDK.
   */
  card?: Card;
}

export const retrieveCardResponseSchema: Schema<RetrieveCardResponse> = object({
  errors: ['errors', optional(array(lazy(() => errorSchema)))],
  card: ['card', optional(lazy(() => cardSchema))],
});
