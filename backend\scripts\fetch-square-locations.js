#!/usr/bin/env node

/**
 * Square Locations Fetcher Script
 * 
 * This script fetches all Square locations for your account
 * and displays them in a readable format.
 * 
 * Usage:
 *   node scripts/fetch-square-locations.js
 * 
 * Make sure you have your Square credentials in the .env file:
 *   SQUARE_ACCESS_TOKEN=your_access_token
 *   SQUARE_APPLICATION_ID=your_application_id
 *   SQUARE_ENVIRONMENT=sandbox (or production)
 */

const { Client, Environment } = require('square');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = path.join(__dirname, '..', '.env');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env file not found at:', envPath);
    console.error('Please create a .env file with your Square credentials');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  return envVars;
}

async function fetchSquareLocations() {
  console.log('🔍 Fetching Square Locations...\n');

  try {
    // Load environment variables
    const env = loadEnvFile();
    
    const accessToken = env.SQUARE_ACCESS_TOKEN;
    const applicationId = env.SQUARE_APPLICATION_ID;
    const environment = env.SQUARE_ENVIRONMENT || 'sandbox';

    if (!accessToken || accessToken === 'your_square_access_token') {
      console.error('❌ SQUARE_ACCESS_TOKEN not found or not set in .env file');
      process.exit(1);
    }

    if (!applicationId || applicationId === 'your_square_application_id') {
      console.error('❌ SQUARE_APPLICATION_ID not found or not set in .env file');
      process.exit(1);
    }

    console.log('📋 Configuration:');
    console.log(`   Environment: ${environment}`);
    console.log(`   Application ID: ${applicationId}`);
    console.log(`   Access Token: ${accessToken.substring(0, 10)}...`);
    console.log('');

    // Initialize Square client
    const client = new Client({
      accessToken: accessToken,
      environment: environment === 'production' ? Environment.Production : Environment.Sandbox,
    });

    const { locationsApi } = client;

    // Fetch locations
    console.log('🌐 Calling Square Locations API...');
    const response = await locationsApi.listLocations();

    if (response.result.errors && response.result.errors.length > 0) {
      console.error('❌ Square API Error:');
      response.result.errors.forEach(error => {
        console.error(`   ${error.category}: ${error.detail}`);
      });
      process.exit(1);
    }

    const locations = response.result.locations || [];

    if (locations.length === 0) {
      console.log('⚠️  No locations found for your Square account');
      return;
    }

    console.log(`✅ Found ${locations.length} location(s):\n`);

    // Display locations in a readable format
    locations.forEach((location, index) => {
      console.log(`📍 Location ${index + 1}:`);
      console.log(`   ID: ${location.id}`);
      console.log(`   Name: ${location.name || 'N/A'}`);
      console.log(`   Status: ${location.status || 'N/A'}`);
      console.log(`   Type: ${location.type || 'N/A'}`);
      
      if (location.address) {
        console.log(`   Address: ${location.address.addressLine1 || ''} ${location.address.addressLine2 || ''}`.trim());
        console.log(`   City: ${location.address.locality || 'N/A'}`);
        console.log(`   State: ${location.address.administrativeDistrictLevel1 || 'N/A'}`);
        console.log(`   Country: ${location.address.country || 'N/A'}`);
        console.log(`   Postal Code: ${location.address.postalCode || 'N/A'}`);
      }
      
      if (location.timezone) {
        console.log(`   Timezone: ${location.timezone}`);
      }
      
      if (location.capabilities && location.capabilities.length > 0) {
        console.log(`   Capabilities: ${location.capabilities.join(', ')}`);
      }
      
      console.log('');
    });

    // Show current configuration
    const currentLocationId = env.SQUARE_LOCATION_ID;
    if (currentLocationId && currentLocationId !== 'your_square_location_id') {
      console.log(`🔧 Current SQUARE_LOCATION_ID in .env: ${currentLocationId}`);
      
      const currentLocation = locations.find(loc => loc.id === currentLocationId);
      if (currentLocation) {
        console.log(`   ✅ This location exists: ${currentLocation.name || 'Unnamed'}`);
      } else {
        console.log(`   ⚠️  This location ID was not found in your account`);
      }
    } else {
      console.log('🔧 To use Square payments, add one of these location IDs to your .env file:');
      console.log('   SQUARE_LOCATION_ID=<location_id>');
    }

    console.log('\n✨ Done!');

  } catch (error) {
    console.error('❌ Error fetching Square locations:');
    console.error(error.message);
    
    if (error.errors) {
      error.errors.forEach(err => {
        console.error(`   ${err.category}: ${err.detail}`);
      });
    }
    
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  fetchSquareLocations();
}

module.exports = { fetchSquareLocations };
