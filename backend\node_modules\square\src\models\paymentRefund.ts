import {
  array,
  boolean,
  lazy,
  nullable,
  object,
  optional,
  Schema,
  string,
} from '../schema';
import {
  DestinationDetails,
  destinationDetailsSchema,
} from './destinationDetails';
import { Money, moneySchema } from './money';
import { ProcessingFee, processingFeeSchema } from './processingFee';

/**
 * Represents a refund of a payment made using Square. Contains information about
 * the original payment and the amount of money refunded.
 */
export interface PaymentRefund {
  /** The unique ID for this refund, generated by Square. */
  id: string;
  /**
   * The refund's status:
   * - `PENDING` - Awaiting approval.
   * - `COMPLETED` - Successfully completed.
   * - `REJECTED` - The refund was rejected.
   * - `FAILED` - An error occurred.
   */
  status?: string | null;
  /** The location ID associated with the payment this refund is attached to. */
  locationId?: string | null;
  /** Flag indicating whether or not the refund is linked to an existing payment in Square. */
  unlinked?: boolean;
  /**
   * The destination type for this refund.
   * Current values include `CARD`, `BANK_ACCOUNT`, `WALLET`, `BUY_NOW_PAY_LATER`, `CASH`,
   * `EXTERNAL`, and `SQUARE_ACCOUNT`.
   */
  destinationType?: string | null;
  /** Details about a refund's destination. */
  destinationDetails?: DestinationDetails;
  /**
   * Represents an amount of money. `Money` fields can be signed or unsigned.
   * Fields that do not explicitly define whether they are signed or unsigned are
   * considered unsigned and can only hold positive amounts. For signed fields, the
   * sign of the value indicates the purpose of the money transfer. See
   * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-monetary-amounts)
   * for more information.
   */
  amountMoney: Money;
  /**
   * Represents an amount of money. `Money` fields can be signed or unsigned.
   * Fields that do not explicitly define whether they are signed or unsigned are
   * considered unsigned and can only hold positive amounts. For signed fields, the
   * sign of the value indicates the purpose of the money transfer. See
   * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-monetary-amounts)
   * for more information.
   */
  appFeeMoney?: Money;
  /** Processing fees and fee adjustments assessed by Square for this refund. */
  processingFee?: ProcessingFee[] | null;
  /** The ID of the payment associated with this refund. */
  paymentId?: string | null;
  /** The ID of the order associated with the refund. */
  orderId?: string | null;
  /** The reason for the refund. */
  reason?: string | null;
  /** The timestamp of when the refund was created, in RFC 3339 format. */
  createdAt?: string;
  /** The timestamp of when the refund was last updated, in RFC 3339 format. */
  updatedAt?: string;
  /** An optional ID of the team member associated with taking the payment. */
  teamMemberId?: string;
  /** An optional ID for a Terminal refund. */
  terminalRefundId?: string;
}

export const paymentRefundSchema: Schema<PaymentRefund> = object({
  id: ['id', string()],
  status: ['status', optional(nullable(string()))],
  locationId: ['location_id', optional(nullable(string()))],
  unlinked: ['unlinked', optional(boolean())],
  destinationType: ['destination_type', optional(nullable(string()))],
  destinationDetails: [
    'destination_details',
    optional(lazy(() => destinationDetailsSchema)),
  ],
  amountMoney: ['amount_money', lazy(() => moneySchema)],
  appFeeMoney: ['app_fee_money', optional(lazy(() => moneySchema))],
  processingFee: [
    'processing_fee',
    optional(nullable(array(lazy(() => processingFeeSchema)))),
  ],
  paymentId: ['payment_id', optional(nullable(string()))],
  orderId: ['order_id', optional(nullable(string()))],
  reason: ['reason', optional(nullable(string()))],
  createdAt: ['created_at', optional(string())],
  updatedAt: ['updated_at', optional(string())],
  teamMemberId: ['team_member_id', optional(string())],
  terminalRefundId: ['terminal_refund_id', optional(string())],
});
