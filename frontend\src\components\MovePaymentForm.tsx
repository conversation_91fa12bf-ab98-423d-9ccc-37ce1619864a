/**
 * Move Payment Form Component
 *
 * Provides Move Payment Gateway integration for payment processing
 * Maintains POS terminal UI design with mobile responsiveness
 */

import { useState } from 'react';
import { toast } from 'sonner';
import { useMovePaymentFlow, useMovePaymentUtils } from '../hooks/useMove';

import { AndroidNumericKeypad } from './AndroidNumericKeypad';

interface MovePaymentFormProps {
  amount?: number; // Amount in cents (optional, defaults to 1000)
  currency?: string;
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  customerData?: {
    firstName?: string;
    lastName?: string;
    email?: string;
  };
  className?: string;
}

export function MovePaymentForm({
  amount: initialAmount,
  currency = 'EUR',
  onSuccess = () => {},
  onError = () => {},
  onCancel,
  customerData,
  className = ''
}: MovePaymentFormProps) {
  const [amount, setAmount] = useState(initialAmount || 0); // Start with 0 if no amount provided
  const [amountInput, setAmountInput] = useState(initialAmount ? (initialAmount / 100).toFixed(2) : ''); // Start empty
  // const [paymentData, setPaymentData] = useState<any>(null);
  // const [qrData, setQrData] = useState<any>(null);
  const [payerName, setPayerName] = useState(
    customerData?.firstName && customerData?.lastName
      ? `${customerData.firstName} ${customerData.lastName}`
      : ''
  );
  const [showOptions, setShowOptions] = useState(false);


  const { createPayment, isLoading, error } = useMovePaymentFlow();
  const { generateOrderId, getSuccessUrl, getCancelUrl } = useMovePaymentUtils();

  // Helper function to handle amount changes from keyboard
  const handleAmountChange = (value: string) => {
    setAmountInput(value);
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue >= 0) {
      setAmount(Math.round(numericValue * 100)); // Convert to cents
    }
  };

  // const handleSuccess = (result: any) => {
  //   toast.success('Payment completed successfully!');
  //   onSuccess?.(result);
  // };

  // const handleError = (error: string) => {
  //   toast.error(`Payment failed: ${error}`);
  //   onError?.(error);
  // };

  // const handleCancel = () => {
  //   setShowOverlay(false);
  //   setPaymentData(null);
  //   setShowOptions(false);
  //   // setQrData(null);
  //   onCancel?.();
  // };

  const handleGetQRCode = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (isLoading) {
      console.log('Request already in progress, ignoring duplicate call');
      return;
    }

    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount, // Use raw amount in cents
        payerName: payerName.trim(),
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'qr', // Request QR code type
      });

      if (result.success && result.data) {
        // setPaymentData(result.data);
        // setShowOverlay(true); // Use overlay instead of standalone QR
        setShowOptions(false);
        toast.success('QR Code generated successfully!');
      } else {
        toast.error(result.message || 'Failed to generate QR code');
      }
    } catch (error) {
      console.error('QR generation error:', error);
      toast.error('Failed to generate QR code');
    }
  };

  const handleGetURL = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (isLoading) {
      console.log('Request already in progress, ignoring duplicate call');
      return;
    }

    try {
      // Loading state is managed by the hook
      const orderId = generateOrderId();

      console.log('=== CREATING MOVE PAYMENT URL ===');
      console.log('Order ID:', orderId);
      console.log('Amount:', amount);
      console.log('Payer Name:', payerName.trim());

      const result = await createPayment({
        orderId,
        amount: amount, // Use raw amount in cents
        payerName: payerName.trim(),
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url', // Request payment URL type
      });

      if (result.success && result.data) {
        console.log('=== MOVE PAYMENT URL GENERATED ===');
        console.log('Payment URL:', result.data.redirect_url);
        console.log('Token:', result.data.token);
        console.log('Order ID:', result.data.order_id);
        console.log('=== END MOVE PAYMENT URL ===');

        // Open payment URL in the same tab
        window.location.href = result.data.redirect_url;
        toast.success('Redirecting to payment page...');

        // Reset form state
        setShowOptions(false);
      } else {
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('URL generation error:', error);
      toast.error('Failed to generate payment URL');
    } finally {
      // Loading state is managed by the hook
    }
  };

  const handleStartPayment = async () => {
    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (isLoading) {
      console.log('Request already in progress, ignoring duplicate call');
      return;
    }

    try {
      const orderId = generateOrderId();
      
      const paymentRequest = {
        orderId,
        amount,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        payerName: payerName.trim(),
        languageCode: 'en',
      };

      const result = await createPayment({
        ...paymentRequest,
        type: 'qr', // Request QR code for overlay
      });

      if (!result.success || !result.data) {
        throw new Error(result.message || 'Failed to create payment');
      }

      // Open payment URL in the same tab
      window.location.href = result.data.redirect_url;
      toast.success('Redirecting to payment page...');
      setShowOptions(false);
    } catch (error) {
      console.error('Failed to start Move payment:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to start payment');
    }
  };

  // Removed standalone QR display to prevent conflicts with overlay

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 font-['Poppins'] mb-2">
          Move Payment
        </h2>
        <p className="text-gray-600 font-['Poppins']">
          Secure payment processing
        </p>
      </div>

      {/* Amount Input with Keyboard */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
          Amount ({currency}) *
        </label>
        <AndroidNumericKeypad
          value={amountInput}
          onChange={handleAmountChange}
          placeholder="0.00"
          maxLength={8}
          allowDecimal={true}
          className="w-full"
        />
      </div>

      {/* Payer Name Input */}
      <div className="mb-6">
        <label htmlFor="payerName" className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
          Payer Name *
        </label>
        <input
          type="text"
          id="payerName"
          value={payerName}
          onChange={(e) => setPayerName(e.target.value)}
          placeholder="Enter your full name"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-['Poppins']"
          required
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600 font-['Poppins']">
            {error instanceof Error ? error.message : 'An error occurred'}
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        {!showOptions ? (
          <>
            <button
              onClick={handleGetURL}
              disabled={isLoading || !payerName.trim() || amount <= 0}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Generating...
                </div>
              ) : (
                'Get Payment URL'
              )}
            </button>

            <button
              onClick={() => setShowOptions(true)}
              disabled={isLoading || !payerName.trim() || amount <= 0}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
            >
              More Options
            </button>
          </>
        ) : (
          <>
            <button
              onClick={handleGetQRCode}
              disabled={isLoading || !payerName.trim() || amount <= 0}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Generating...
                </div>
              ) : (
                'Get QR Code'
              )}
            </button>

            <button
              onClick={handleStartPayment}
              disabled={isLoading || !payerName.trim() || amount <= 0}
              className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Creating Payment...
                </div>
              ) : (
                'Show QR Code Overlay'
              )}
            </button>

            <button
              onClick={() => setShowOptions(false)}
              disabled={isLoading}
              className="w-full bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
            >
              Back
            </button>
          </>
        )}

        {onCancel && (
          <button
            onClick={onCancel}
            disabled={isLoading}
            className="w-full bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:cursor-not-allowed text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 font-['Poppins']"
          >
            Cancel
          </button>
        )}
      </div>

      {/* Info Section */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-sm font-semibold text-blue-900 font-['Poppins'] mb-2">
          How it works:
        </h3>
        <ul className="text-xs text-blue-800 font-['Poppins'] space-y-1">
          <li>• Enter your name and click "Generate QR Code"</li>
          <li>• Scan the QR code with your Move app</li>
          <li>• Complete the payment in your app</li>
          <li>• You'll be redirected back automatically</li>
        </ul>
      </div>

      {/* Move Payment Branding */}
      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 font-['Poppins']">
          Powered by Move Payment Gateway
        </p>
      </div>


    </div>
  );
}
