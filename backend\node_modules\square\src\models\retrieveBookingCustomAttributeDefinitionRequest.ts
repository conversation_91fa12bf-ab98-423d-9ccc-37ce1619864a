import { number, object, optional, Schema } from '../schema';

/** Represents a [RetrieveBookingCustomAttributeDefinition]($e/BookingCustomAttributes/RetrieveBookingCustomAttributeDefinition) request. */
export interface RetrieveBookingCustomAttributeDefinitionRequest {
  /**
   * The current version of the custom attribute definition, which is used for strongly consistent
   * reads to guarantee that you receive the most up-to-date data. When included in the request,
   * <PERSON> returns the specified version or a higher version if one exists. If the specified version
   * is higher than the current version, <PERSON> returns a `BAD_REQUEST` error.
   */
  version?: number;
}

export const retrieveBookingCustomAttributeDefinitionRequestSchema: Schema<RetrieveBookingCustomAttributeDefinitionRequest> = object(
  { version: ['version', optional(number())] }
);
