import { Schema } from '../schema';
import { DeviceCheckoutOptions } from './deviceCheckoutOptions';
import { Money } from './money';
import { PaymentOptions } from './paymentOptions';
/** Represents a checkout processed by the Square Terminal. */
export interface TerminalCheckout {
    /** A unique ID for this `TerminalCheckout`. */
    id?: string;
    /**
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-monetary-amounts)
     * for more information.
     */
    amountMoney: Money;
    /**
     * An optional user-defined reference ID that can be used to associate
     * this `TerminalCheckout` to another entity in an external system. For example, an order
     * ID generated by a third-party shopping cart. The ID is also associated with any payments
     * used to complete the checkout.
     */
    referenceId?: string | null;
    /**
     * An optional note to associate with the checkout, as well as with any payments used to complete the checkout.
     * Note: maximum 500 characters
     */
    note?: string | null;
    /** The reference to the Square order ID for the checkout request. Supported only in the US. */
    orderId?: string | null;
    paymentOptions?: PaymentOptions;
    deviceOptions: DeviceCheckoutOptions;
    /**
     * An RFC 3339 duration, after which the checkout is automatically canceled.
     * A `TerminalCheckout` that is `PENDING` is automatically `CANCELED` and has a cancellation reason
     * of `TIMED_OUT`.
     * Default: 5 minutes from creation
     * Maximum: 5 minutes
     */
    deadlineDuration?: string | null;
    /**
     * The status of the `TerminalCheckout`.
     * Options: `PENDING`, `IN_PROGRESS`, `CANCEL_REQUESTED`, `CANCELED`, `COMPLETED`
     */
    status?: string;
    cancelReason?: string;
    /** A list of IDs for payments created by this `TerminalCheckout`. */
    paymentIds?: string[];
    /** The time when the `TerminalCheckout` was created, as an RFC 3339 timestamp. */
    createdAt?: string;
    /** The time when the `TerminalCheckout` was last updated, as an RFC 3339 timestamp. */
    updatedAt?: string;
    /** The ID of the application that created the checkout. */
    appId?: string;
    /** The location of the device where the `TerminalCheckout` was directed. */
    locationId?: string;
    paymentType?: string;
    /** An optional ID of the team member associated with creating the checkout. */
    teamMemberId?: string | null;
    /** An optional ID of the customer associated with the checkout. */
    customerId?: string | null;
    /**
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-monetary-amounts)
     * for more information.
     */
    appFeeMoney?: Money;
    /**
     * Optional additional payment information to include on the customer's card statement as
     * part of the statement description. This can be, for example, an invoice number, ticket number,
     * or short description that uniquely identifies the purchase. Supported only in the US.
     */
    statementDescriptionIdentifier?: string | null;
    /**
     * Represents an amount of money. `Money` fields can be signed or unsigned.
     * Fields that do not explicitly define whether they are signed or unsigned are
     * considered unsigned and can only hold positive amounts. For signed fields, the
     * sign of the value indicates the purpose of the money transfer. See
     * [Working with Monetary Amounts](https://developer.squareup.com/docs/build-basics/working-with-monetary-amounts)
     * for more information.
     */
    tipMoney?: Money;
}
export declare const terminalCheckoutSchema: Schema<TerminalCheckout>;
