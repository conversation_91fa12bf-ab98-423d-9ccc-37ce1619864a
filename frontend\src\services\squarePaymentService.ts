import { apiClient } from './api';

export interface SquarePaymentRequest {
  orderId: string;
  amount: number;
  currency?: string;
  payerName: string;
  payerEmail?: string;
  successUrl: string;
  cancelUrl: string;
  languageCode?: string;
  type?: 'qr' | 'url';
}

export interface SquarePaymentResponse {
  success: boolean;
  data?: {
    transaction_id: string;
    order_id: string;
    payment_id: string;
    checkout_url?: string;
    redirect_url?: string;
    qr_code_data?: string;
    amount: number;
    currency: string;
    type: string;
  };
  error?: string;
  message?: string;
}

export interface SquareStatusResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface SquareHealthResponse {
  success: boolean;
  data?: {
    status: string;
  };
  error?: string;
  message?: string;
}

class SquarePaymentService {
  private baseUrl = '/square';

  /**
   * Create a new Square payment
   */
  async createPayment(params: SquarePaymentRequest): Promise<SquarePaymentResponse> {
    try {
      console.log('Creating Square payment with params:', params);
      
      const response = await apiClient.post(`${this.baseUrl}/payment`, params);
      
      console.log('Square payment response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Square payment creation failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to connect to Square payment service',
      };
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(orderId: string): Promise<SquareStatusResponse> {
    try {
      console.log('Getting Square payment status for order:', orderId);
      
      const response = await apiClient.get(`${this.baseUrl}/payment/${orderId}/status`);
      
      console.log('Square payment status response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Square payment status retrieval failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to get payment status from Square service',
      };
    }
  }

  /**
   * Health check for Square service
   */
  async healthCheck(): Promise<SquareHealthResponse> {
    try {
      console.log('Performing Square health check');
      
      const response = await apiClient.get(`${this.baseUrl}/health`);
      
      console.log('Square health check response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Square health check failed:', error);
      
      if (error.response?.data) {
        return error.response.data;
      }
      
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Failed to connect to Square service',
      };
    }
  }

  /**
   * Generate QR code data URL for payment
   */
  generateQRCodeDataUrl(paymentUrl: string): string {
    return paymentUrl;
  }

  /**
   * Get supported payment methods
   */
  getSupportedPaymentMethods(): string[] {
    return ['card', 'digital_wallet', 'ach'];
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[] {
    return ['USD', 'GBP', 'EUR', 'CAD', 'AUD'];
  }

  /**
   * Validate payment amount
   */
  validateAmount(amount: number, currency: string = 'GBP'): boolean {
    if (amount <= 0) return false;
    
    // Minimum amounts by currency (in cents)
    const minimums: Record<string, number> = {
      USD: 50,   // $0.50
      GBP: 30,   // £0.30
      EUR: 50,   // €0.50
      CAD: 50,   // C$0.50
      AUD: 50,   // A$0.50
    };
    
    const minimum = minimums[currency] || 50;
    return amount >= minimum;
  }

  /**
   * Format amount for display
   */
  formatAmount(amount: number, currency: string = 'GBP'): string {
    const value = amount / 100;
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currency,
    }).format(value);
  }
}

// Export singleton instance
export const squarePaymentService = new SquarePaymentService();
