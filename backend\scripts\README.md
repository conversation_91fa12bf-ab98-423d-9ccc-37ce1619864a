# Square Scripts

This directory contains utility scripts for working with Square API.

## fetch-square-locations

Fetches all Square locations for your account and displays them in a readable format.

### Prerequisites

Make sure you have your Square credentials configured in the `.env` file:

```env
SQUARE_ACCESS_TOKEN=your_access_token
SQUARE_APPLICATION_ID=your_application_id
SQUARE_ENVIRONMENT=sandbox
```

### Usage

#### Option 1: Using npm scripts (Recommended)

```bash
# JavaScript version
npm run square:locations

# TypeScript version
npm run square:locations:ts
```

#### Option 2: Direct execution

```bash
# JavaScript version
node scripts/fetch-square-locations.js

# TypeScript version
npx tsx scripts/fetch-square-locations.ts
```

### Sample Output

```
🔍 Fetching Square Locations...

📋 Configuration:
   Environment: sandbox
   Application ID: sandbox-sq0idb-gulkfdYQnn8wN9ez9PDWlQ
   Access Token: EAAAlykJst...

🌐 Calling Square Locations API...
✅ Found 1 location(s):

📍 Location 1:
   ID: LNEMG6VQ7GMEE
   Name: Default Test Account
   Status: ACTIVE
   Type: PHYSICAL
   Address: 1455 Market St
   City: San Francisco
   State: CA
   Country: US
   Postal Code: 94103
   Timezone: America/Los_Angeles
   Capabilities: CREDIT_CARD_PROCESSING

🔧 Current SQUARE_LOCATION_ID in .env: LNEMG6VQ7GMEE
   ✅ This location exists: Default Test Account

💡 Recommended location ID: LNEMG6VQ7GMEE
   Name: Default Test Account
   Add this to your .env file: SQUARE_LOCATION_ID=LNEMG6VQ7GMEE

✨ Done!
```

### What the script does

1. **Loads your Square credentials** from the `.env` file
2. **Validates the credentials** are properly configured
3. **Calls the Square Locations API** to fetch all locations
4. **Displays each location** with detailed information:
   - Location ID (needed for payments)
   - Name and status
   - Address information
   - Capabilities
5. **Shows your current configuration** and validates it
6. **Recommends a location ID** if you don't have one set

### Troubleshooting

#### "Access token not found"
Make sure your `.env` file has the correct `SQUARE_ACCESS_TOKEN` value.

#### "No locations found"
This could mean:
- Your Square account doesn't have any locations set up
- Your access token doesn't have the right permissions
- You're using the wrong environment (sandbox vs production)

#### "Invalid credentials"
Double-check your `SQUARE_ACCESS_TOKEN` and `SQUARE_APPLICATION_ID` in the Square Developer Dashboard.

### Finding Your Credentials

1. Go to [developer.squareup.com](https://developer.squareup.com)
2. Select your application
3. Go to the **Sandbox** or **Production** tab
4. Copy the **Application ID** and **Access Token**
