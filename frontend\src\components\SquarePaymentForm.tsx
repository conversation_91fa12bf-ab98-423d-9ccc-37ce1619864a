import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { ExternalLink } from 'lucide-react';
import { useSquare } from '../hooks/useSquare';
import { generateOrderId } from '../utils/paymentUtils';
import { AndroidNumericKeypad } from './AndroidNumericKeypad';

// Square Web Payments SDK types
declare global {
  interface Window {
    Square?: any;
  }
}

interface SquarePaymentFormProps {
  onSuccess?: (result: any) => void;
  onError?: (error: any) => void;
  className?: string;
}

const SquarePaymentForm: React.FC<SquarePaymentFormProps> = ({
  onSuccess,
  onError,
  className = '',
}) => {
  const [amount, setAmount] = useState<number>(0);
  const [amountInput, setAmountInput] = useState(''); // Start empty like Move form
  const [payerName, setPayerName] = useState('');
  const [payerEmail, setPayerEmail] = useState('');
  const [selectedCurrency] = useState('USD');
  const [showOptions, setShowOptions] = useState(false);

  const { createPayment, getPaymentStatus, isLoading } = useSquare();

  const getSuccessUrl = () => `${window.location.origin}/payment/success`;
  const getCancelUrl = () => `${window.location.origin}/payment/cancel`;

  // Helper function to handle amount changes from keyboard
  const handleAmountChange = (value: string) => {
    setAmountInput(value);
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue >= 0) {
      setAmount(Math.round(numericValue * 100)); // Convert to cents
    }
  };

  // Square now uses hosted checkout - no SDK initialization needed

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!payerName.trim()) {
      toast.error('Please enter payer name');
      return;
    }

    setShowOptions(true);
  };



  const handlePaymentUrl = async () => {
    try {
      const orderId = generateOrderId();
      const result = await createPayment({
        orderId,
        amount: amount,
        currency: selectedCurrency,
        payerName: payerName.trim(),
        payerEmail: payerEmail.trim() || undefined,
        successUrl: getSuccessUrl(),
        cancelUrl: getCancelUrl(),
        type: 'url',
      });

      if (result.success && result.data) {
        console.log('=== SQUARE PAYMENT URL GENERATED ===');
        console.log('Payment URL:', result.data.redirect_url);
        console.log('Payment ID:', result.data.payment_id);
        console.log('Order ID:', result.data.order_id);
        console.log('=== END SQUARE PAYMENT URL ===');

        // Open payment URL in the same tab
        window.location.href = result.data.redirect_url;
        toast.success('Redirecting to payment page...');

        // Reset form state
        setShowOptions(false);
      } else {
        toast.error(result.message || 'Failed to generate payment URL');
      }
    } catch (error) {
      console.error('Payment URL generation error:', error);
      toast.error('Failed to generate payment URL');
    }
  };



  // const copyToClipboard = async (text: string) => {
  //   try {
  //     await navigator.clipboard.writeText(text);
  //     toast.success('Copied to clipboard!');
  //   } catch (error) {
  //     toast.error('Failed to copy to clipboard');
  //   }
  // };

  // const handleSuccess = (result: any) => {
  //   toast.success('Payment completed successfully!');
  //   onSuccess?.(result);
  // };

  // const handleError = (error: any) => {
  //   toast.error('Payment failed');
  //   onError?.(error);
  // };

  return (
    <div className={`bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto ${className}`}>
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 font-['Poppins'] mb-2">
          Square Payment
        </h2>
        <p className="text-gray-600 font-['Poppins']">
          Secure payment processing
        </p>
      </div>

      {!showOptions ? (
        <div className="space-y-6">
          {/* Amount Input with Keyboard */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Amount ({selectedCurrency}) *
            </label>
            <AndroidNumericKeypad
              value={amountInput}
              onChange={handleAmountChange}
              placeholder="0.00"
              maxLength={8}
              allowDecimal={true}
              className="w-full"
            />
          </div>

          {/* Payer Name */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Payer Name *
            </label>
            <input
              type="text"
              placeholder="Enter payer name"
              className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={payerName}
              onChange={(e) => setPayerName(e.target.value)}
              disabled={isLoading}
            />
          </div>

          {/* Email (Optional) */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 font-['Poppins'] mb-2">
              Email (Optional)
            </label>
            <input
              type="email"
              placeholder="Enter email address"
              className="w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={payerEmail}
              onChange={(e) => setPayerEmail(e.target.value)}
              disabled={isLoading}
            />
          </div>

          <button
            onClick={handleSubmit}
            disabled={isLoading || amount <= 0 || !payerName.trim()}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
          >
            {isLoading ? 'Processing...' : `Continue with ${selectedCurrency}$${(amount / 100).toFixed(2)}`}
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="text-center">
            <h4 className="text-lg font-medium text-gray-900 mb-2 font-['Poppins']">
              Payment Amount: {selectedCurrency}${(amount / 100).toFixed(2)}
            </h4>
            <p className="text-sm text-gray-600 mb-4 font-['Poppins']">Choose your payment method</p>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <button
              onClick={handlePaymentUrl}
              disabled={isLoading}
              className="flex items-center justify-center gap-3 w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed font-['Poppins'] font-medium"
            >
              <ExternalLink className="w-5 h-5" />
              {isLoading ? 'Opening...' : 'Open Payment Page'}
            </button>
          </div>

          <button
            onClick={() => setShowOptions(false)}
            className="w-full text-gray-600 py-2 px-4 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 font-['Poppins']"
          >
            Back to Form
          </button>
        </div>
      )}

      {/* Square now uses hosted checkout - no modal needed */}
    </div>
  );
};

export default SquarePaymentForm;
